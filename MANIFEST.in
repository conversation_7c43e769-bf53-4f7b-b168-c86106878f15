# Include documentation
include README.md
include LICENSE
include CHANGELOG.md

# Include requirements
include requirements.txt

# Include examples
recursive-include examples *.py
recursive-include examples *.md

# Include documentation
recursive-include docs *.md
recursive-include docs *.rst

# Include templates
recursive-include pyobfuscator/templates *.py

# Exclude development files
exclude .gitignore
exclude .github
recursive-exclude tests *
recursive-exclude .git *
recursive-exclude __pycache__ *
recursive-exclude *.pyc
recursive-exclude .pytest_cache *
