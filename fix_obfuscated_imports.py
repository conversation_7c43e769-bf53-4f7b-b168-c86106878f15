#!/usr/bin/env python3
"""
Fix import issues in obfuscated files
This script helps fix obfuscated files that can't find their modules
"""

import os
import sys
import json
from pathlib import Path

def create_launcher_script(obfuscated_files_dir: str):
    """
    Create a launcher script that can run obfuscated files with proper imports
    
    Args:
        obfuscated_files_dir: Directory containing obfuscated files
    """
    
    obfuscated_files = []
    main_file = None
    
    # Find all obfuscated files
    for file in Path(obfuscated_files_dir).glob("*.py"):
        if "obfuscated" in file.name:
            obfuscated_files.append(file)
            
            # Try to identify main file
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'if __name__ == "__main__"' in content:
                    main_file = file
    
    if not main_file and obfuscated_files:
        main_file = obfuscated_files[0]  # Use first file as main
    
    if not main_file:
        print("❌ No obfuscated files found!")
        return None
    
    # Create launcher script
    launcher_content = f'''#!/usr/bin/env python3
"""
Launcher for Obfuscated Application
Generated by ObfusLite Fix Tool
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """Setup environment for obfuscated modules"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set working directory
    os.chdir(current_dir)

def load_and_run_obfuscated():
    """Load and run the main obfuscated file"""
    setup_environment()
    
    # Import and run main obfuscated file
    main_file = "{main_file.name}"
    
    print(f"🚀 Loading obfuscated application: {{main_file}}")
    
    try:
        # Read and execute the obfuscated file
        with open(main_file, 'r', encoding='utf-8') as f:
            obfuscated_code = f.read()
        
        # Execute in global namespace
        exec(obfuscated_code, globals())
        
    except Exception as e:
        print(f"❌ Error running obfuscated application: {{e}}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    load_and_run_obfuscated()
'''
    
    launcher_file = Path(obfuscated_files_dir) / "run_obfuscated.py"
    with open(launcher_file, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✅ Launcher created: {launcher_file}")
    print(f"🚀 Usage: python {launcher_file}")
    
    return launcher_file

def create_single_executable(obfuscated_files_dir: str):
    """
    Combine all obfuscated files into a single executable
    
    Args:
        obfuscated_files_dir: Directory containing obfuscated files
    """
    
    obfuscated_files = list(Path(obfuscated_files_dir).glob("*obfuscated*.py"))
    
    if not obfuscated_files:
        print("❌ No obfuscated files found!")
        return None
    
    print(f"📁 Found {len(obfuscated_files)} obfuscated files")
    
    # Try to deobfuscate and recombine
    try:
        from obfuslite import Obfuscator
        
        combined_code = []
        combined_code.append('#!/usr/bin/env python3')
        combined_code.append('"""Combined Obfuscated Application"""')
        combined_code.append('')
        
        obfuscator = Obfuscator()
        
        for obf_file in obfuscated_files:
            print(f"   Processing: {obf_file.name}")
            
            try:
                # Read obfuscated file
                with open(obf_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Try to extract the obfuscated data
                if '_decode_and_execute()' in content:
                    # This is a standalone obfuscated file
                    # Extract the obfuscated_info
                    start = content.find('obfuscated_info = {')
                    if start != -1:
                        end = content.find('\\n    ', start)
                        if end != -1:
                            obf_data_str = content[start:end].replace('obfuscated_info = ', '')
                            try:
                                obf_data = eval(obf_data_str)
                                original_code = obfuscator.deobfuscate(obf_data)
                                combined_code.append(f"\\n# === Code from {obf_file.name} ===")
                                combined_code.append(original_code)
                                continue
                            except:
                                pass
                
                # If extraction failed, include the file as-is
                combined_code.append(f"\\n# === Obfuscated code from {obf_file.name} ===")
                combined_code.append(content)
                
            except Exception as e:
                print(f"   Warning: Could not process {obf_file.name}: {e}")
        
        # Create combined file
        combined_file = Path(obfuscated_files_dir) / "combined_application.py"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write('\\n'.join(combined_code))
        
        print(f"✅ Combined file created: {combined_file}")
        return combined_file
        
    except ImportError:
        print("❌ ObfusLite not available for deobfuscation")
        return create_launcher_script(obfuscated_files_dir)

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python fix_obfuscated_imports.py <obfuscated_files_directory>")
        print("Example: python fix_obfuscated_imports.py ./obfuscated_app")
        return
    
    obfuscated_dir = sys.argv[1]
    
    if not os.path.exists(obfuscated_dir):
        print(f"❌ Directory not found: {obfuscated_dir}")
        return
    
    print("🔧 ObfusLite Import Fix Tool")
    print("=" * 40)
    
    # Try to create a single executable
    result = create_single_executable(obfuscated_dir)
    
    if not result:
        # Fallback to launcher script
        result = create_launcher_script(obfuscated_dir)
    
    if result:
        print(f"\\n🎉 Fix completed!")
        print(f"📁 Output: {result}")
        print(f"\\n🚀 Usage:")
        print(f"   python {result}")
        print(f"   pyinstaller --onefile {result}")

if __name__ == "__main__":
    main()
