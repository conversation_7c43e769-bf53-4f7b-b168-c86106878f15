# 🛡️ ObfusLite - Advanced Python Code Obfuscation

**ObfusLite** is a comprehensive Python code obfuscation library featuring novel encoding techniques and an enhanced GUI with multi-file support. Protect your Python applications with professional-grade obfuscation suitable for PyInstaller compilation.

## ✨ Key Features

### 🚀 **Core Obfuscation**
- **13 Advanced Techniques**: From fast XOR to quantum-inspired encoding
- **Multi-Layer Protection**: Apply multiple obfuscation layers
- **PyInstaller Ready**: Generate standalone executable files
- **Reversible**: Complete deobfuscation support

### 🖥️ **Enhanced GUI Interface**
- **Multi-File Batch Processing**: Process hundreds of files at once
- **Project Management**: Save and load obfuscation projects
- **Code Analysis**: Intelligent recommendations based on code complexity
- **Side-by-Side Comparison**: Compare original vs obfuscated code
- **Template System**: Save and reuse configurations
- **Export Options**: ZIP archives, CSV reports, detailed logs

### ⚡ **Performance Modes**
- **Fast Mode**: Ultra-fast processing for development
- **Balanced Mode**: Optimal speed-security balance
- **Full Mode**: Maximum security with all techniques

## 🔧 Installation

```bash
# Basic installation
pip install obfuslite

# With GUI support
pip install obfuslite[gui]

# Full installation with all features
pip install obfuslite[full]
```

## 🚀 Quick Start

### Command Line Usage

```bash
# Basic obfuscation
obfuslite obfuscate input.py -o output.py

# Advanced obfuscation
obfuslite obfuscate input.py -t fast_xor -l 3 -s 12345

# Launch enhanced GUI
obfuslite gui

# List available techniques
obfuslite list-techniques

# Benchmark performance
obfuslite benchmark input.py
```

### Python API

```python
from obfuslite import Obfuscator, quick_obfuscate

# Quick obfuscation (one line!)
standalone_code = quick_obfuscate(code, technique='fast_xor', layers=2)

# Advanced usage
obfuscator = Obfuscator()
result = obfuscator.obfuscate(code, technique='fast_xor', layers=3)
standalone_code = obfuscator.create_standalone_file(result)

# Save to file
with open('obfuscated.py', 'w') as f:
    f.write(standalone_code)
```

## 🎯 Available Techniques

### ⚡ Fast Techniques (Recommended)
- **fast_xor**: Multi-key XOR with compression
- **fast_base64**: Base64 with character substitution  
- **fast_rotation**: Multi-round Caesar cipher
- **fast_hash**: Hash-based chunk encoding
- **fast_binary**: Binary manipulation with bit shifting
- **fast_lookup**: Character lookup table encoding

### 🔬 Advanced Techniques
- **quantum**: Quantum-inspired encoding with gate operations
- **dna**: DNA sequence mapping with genetic mutations
- **fractal**: Fractal pattern encoding with chaos theory
- **neural**: Neural network weight encoding
- **steganographic**: Steganographic hiding in innocent data
- **runtime**: Runtime reconstruction and self-modification
- **tensor**: Multi-dimensional tensor operations

## 🖥️ Enhanced GUI Features

### **Multi-File Batch Processing**
- Add individual files or entire directories
- Real-time progress tracking
- Automatic ZIP archive creation
- Error handling and reporting

### **Project Management**
- Save projects as `.pyobf` files
- Track file status and modifications
- Centralized configuration management
- Team collaboration support

### **Code Analysis & Recommendations**
- Analyze code complexity and structure
- Get intelligent technique recommendations
- Performance impact assessment
- Security level guidance

### **Comparison Tools**
- Side-by-side code comparison
- Size and complexity statistics
- Readability analysis
- Export comparison reports

## 📊 Performance Comparison

| Technique | Speed | Security | Size Ratio | Use Case |
|-----------|-------|----------|------------|----------|
| fast_xor | ⚡⚡⚡⚡⚡ | 🛡️🛡️🛡️ | 1.2x | Development, Testing |
| fast_base64 | ⚡⚡⚡⚡⚡ | 🛡️🛡️ | 1.4x | Quick Protection |
| quantum | ⚡⚡ | 🛡️🛡️🛡️🛡️🛡️ | 2.1x | Maximum Security |
| neural | ⚡ | 🛡️🛡️🛡️🛡️🛡️ | 2.8x | Research, High-Value |

## 🎮 Examples

### Basic Example
```python
from obfuslite import quick_obfuscate

code = '''
def hello_world():
    print("Hello from ObfusLite!")
    return "Protected!"

if __name__ == "__main__":
    result = hello_world()
    print(f"Result: {result}")
'''

# One-line obfuscation
protected_code = quick_obfuscate(code, technique='fast_xor', layers=2)

# Save and run
with open('protected.py', 'w') as f:
    f.write(protected_code)

# Run: python protected.py
```

### Batch Processing Example
```python
from obfuslite import Obfuscator
import os

obfuscator = Obfuscator()

# Process all Python files in a directory
for root, dirs, files in os.walk('my_project'):
    for file in files:
        if file.endswith('.py'):
            file_path = os.path.join(root, file)
            
            with open(file_path, 'r') as f:
                code = f.read()
            
            result = obfuscator.obfuscate(code, technique='fast_xor')
            standalone = obfuscator.create_standalone_file(result)
            
            output_path = file_path.replace('.py', '_protected.py')
            with open(output_path, 'w') as f:
                f.write(standalone)
```

## 🔒 Security Features

- **Multi-Layer Protection**: Apply multiple obfuscation layers
- **Seed-Based Reproducibility**: Consistent results with seeds
- **Integrity Verification**: Built-in integrity checking
- **Anti-Reverse Engineering**: Advanced techniques resist analysis
- **PyInstaller Compatible**: Works seamlessly with executable creation

## 🛠️ Development

### Running Tests
```bash
# Test basic functionality
python examples/obfuslite_basic_usage.py

# Test GUI
python test_gui.py

# Launch GUI demo
python examples/gui_demo.py
```

### Building from Source
```bash
git clone https://github.com/obfuslite/obfuslite.git
cd obfuslite
pip install -e .
```

## 📋 Requirements

- **Python**: 3.8+
- **Core**: No additional dependencies
- **GUI**: PyQt6 >= 6.4.0
- **Advanced**: NumPy >= 1.21.0, SciPy >= 1.9.0

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

ObfusLite is released under the MIT License. See [LICENSE](LICENSE) for details.

## 🔗 Links

- **Documentation**: [GUI Features Guide](GUI_FEATURES.md)
- **Examples**: [examples/](examples/)
- **Issues**: [GitHub Issues](https://github.com/obfuslite/obfuslite/issues)
- **Discussions**: [GitHub Discussions](https://github.com/obfuslite/obfuslite/discussions)

---

**ObfusLite** - Professional Python Code Protection Made Simple 🛡️
