#!/usr/bin/env python3
"""
<PERSON>ript to combine multiple Python files into one and then obfuscate
This solves the module import issues with obfuscated files
"""

import os
import ast
import sys
from pathlib import Path

def combine_python_files(main_file, output_file="combined_app.py"):
    """
    Combine multiple Python files into a single file
    
    Args:
        main_file: Path to the main Python file
        output_file: Output file name for combined code
    """
    
    def extract_imports_and_code(file_path):
        """Extract imports and code from a Python file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
        except SyntaxError as e:
            print(f"Syntax error in {file_path}: {e}")
            return [], content
        
        imports = []
        other_code = []
        
        for node in tree.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                # Skip relative imports to local modules
                if isinstance(node, ast.ImportFrom):
                    if node.module and (node.module.startswith('.') or 
                                      any(node.module.startswith(local) for local in ['src', 'lib', 'modules'])):
                        continue
                imports.append(ast.unparse(node))
            else:
                other_code.append(ast.unparse(node))
        
        return imports, '\n'.join(other_code)
    
    def find_local_modules(main_file):
        """Find all local Python modules referenced by the main file"""
        main_dir = Path(main_file).parent
        python_files = []
        
        # Find all .py files in the same directory and subdirectories
        for py_file in main_dir.rglob("*.py"):
            if py_file.name != "__init__.py" and py_file != Path(main_file):
                python_files.append(py_file)
        
        return python_files
    
    # Get all Python files
    local_modules = find_local_modules(main_file)
    all_files = [Path(main_file)] + local_modules
    
    print(f"📁 Found {len(all_files)} Python files to combine:")
    for f in all_files:
        print(f"   - {f}")
    
    # Combine all files
    all_imports = set()
    all_code = []
    
    print("\n🔄 Processing files...")
    
    # Process non-main files first
    for file_path in local_modules:
        print(f"   Processing: {file_path}")
        imports, code = extract_imports_and_code(file_path)
        all_imports.update(imports)
        if code.strip():
            all_code.append(f"\n# === Code from {file_path.name} ===")
            all_code.append(code)
    
    # Process main file last
    print(f"   Processing main file: {main_file}")
    main_imports, main_code = extract_imports_and_code(main_file)
    all_imports.update(main_imports)
    all_code.append(f"\n# === Main code from {Path(main_file).name} ===")
    all_code.append(main_code)
    
    # Create combined file
    combined_content = []
    
    # Add header
    combined_content.append('#!/usr/bin/env python3')
    combined_content.append('"""')
    combined_content.append('Combined Python Application')
    combined_content.append('Generated by ObfusLite Combiner')
    combined_content.append('"""')
    combined_content.append('')
    
    # Add all imports
    if all_imports:
        combined_content.append('# === All Imports ===')
        combined_content.extend(sorted(all_imports))
        combined_content.append('')
    
    # Add all code
    combined_content.extend(all_code)
    
    # Write combined file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(combined_content))
    
    print(f"\n✅ Combined file created: {output_file}")
    return output_file

def obfuscate_combined_file(combined_file):
    """Obfuscate the combined file using ObfusLite"""
    try:
        from obfuslite import quick_obfuscate
        
        print(f"\n🔒 Obfuscating {combined_file}...")
        
        # Read combined file
        with open(combined_file, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Obfuscate
        obfuscated_code = quick_obfuscate(
            code, 
            technique='fast_xor',  # Fast and reliable
            layers=2
        )
        
        # Save obfuscated file
        obfuscated_file = combined_file.replace('.py', '_obfuscated.py')
        with open(obfuscated_file, 'w', encoding='utf-8') as f:
            f.write(obfuscated_code)
        
        print(f"✅ Obfuscated file created: {obfuscated_file}")
        return obfuscated_file
        
    except ImportError:
        print("❌ ObfusLite not found. Install with: pip install obfuslite")
        return None
    except Exception as e:
        print(f"❌ Obfuscation failed: {e}")
        return None

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python combine_and_obfuscate.py <main_file.py>")
        print("Example: python combine_and_obfuscate.py app.py")
        return
    
    main_file = sys.argv[1]
    
    if not os.path.exists(main_file):
        print(f"❌ File not found: {main_file}")
        return
    
    print("🚀 ObfusLite Multi-File Combiner & Obfuscator")
    print("=" * 50)
    
    # Step 1: Combine files
    combined_file = combine_python_files(main_file)
    
    # Step 2: Test combined file
    print(f"\n🧪 Testing combined file...")
    try:
        # Try to compile the combined file
        with open(combined_file, 'r') as f:
            compile(f.read(), combined_file, 'exec')
        print("✅ Combined file syntax is valid")
    except SyntaxError as e:
        print(f"❌ Syntax error in combined file: {e}")
        print("Please check the combined file manually")
        return
    
    # Step 3: Obfuscate
    obfuscated_file = obfuscate_combined_file(combined_file)
    
    if obfuscated_file:
        print(f"\n🎉 Success! Your obfuscated application is ready:")
        print(f"   📁 Combined file: {combined_file}")
        print(f"   🔒 Obfuscated file: {obfuscated_file}")
        print(f"\n🚀 Usage:")
        print(f"   python {obfuscated_file}")
        print(f"   pyinstaller --onefile {obfuscated_file}")

if __name__ == "__main__":
    main()
